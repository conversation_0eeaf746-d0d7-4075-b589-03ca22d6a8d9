"""
Test script for E2B Sandbox Agent integration
"""

import asyncio
from dotenv import load_dotenv
from agents.e2b_sandbox_agent import e2b_sandbox_agent, E2BSandboxAgentDeps

load_dotenv()

async def test_e2b_agent():
    """Test the E2B sandbox agent functionality"""
    
    print("🧪 Testing E2B Sandbox Agent...")
    
    # Create dependencies (without websocket for testing)
    deps = E2BSandboxAgentDeps()
    
    # Test 1: Simple Python execution
    print("\n📝 Test 1: Simple Python code execution")
    task1 = """
    Create a simple Python script that:
    1. Calculates the factorial of 10
    2. Creates a list of prime numbers up to 50
    3. Prints the results
    """
    
    try:
        result1 = await e2b_sandbox_agent.run(user_prompt=task1, deps=deps)
        print(f"✅ Test 1 completed successfully!")
        print(f"Dependencies: {result1.data.dependencies}")
        print(f"Code Description: {result1.data.code_description}")
        print(f"Execution Output: {result1.data.execution_output}")
    except Exception as e:
        print(f"❌ Test 1 failed: {e}")
    
    # Test 2: Package installation and usage
    print("\n📦 Test 2: Package installation and data analysis")
    task2 = """
    Create a data analysis script that:
    1. Installs pandas and numpy
    2. Creates a sample dataset with 100 rows of sales data
    3. Calculates basic statistics (mean, median, std)
    4. Shows the first 5 rows of the dataset
    """
    
    try:
        result2 = await e2b_sandbox_agent.run(user_prompt=task2, deps=deps)
        print(f"✅ Test 2 completed successfully!")
        print(f"Dependencies: {result2.data.dependencies}")
        print(f"Code Description: {result2.data.code_description}")
        print(f"Execution Output: {result2.data.execution_output}")
    except Exception as e:
        print(f"❌ Test 2 failed: {e}")
    
    # Test 3: Web request and JSON processing
    print("\n🌐 Test 3: Web request and JSON processing")
    task3 = """
    Create a script that:
    1. Installs the requests library
    2. Makes a GET request to https://jsonplaceholder.typicode.com/posts/1
    3. Parses the JSON response
    4. Prints the title and body of the post
    """
    
    try:
        result3 = await e2b_sandbox_agent.run(user_prompt=task3, deps=deps)
        print(f"✅ Test 3 completed successfully!")
        print(f"Dependencies: {result3.data.dependencies}")
        print(f"Code Description: {result3.data.code_description}")
        print(f"Execution Output: {result3.data.execution_output}")
    except Exception as e:
        print(f"❌ Test 3 failed: {e}")
    
    print("\n🎉 E2B Sandbox Agent testing completed!")

if __name__ == "__main__":
    asyncio.run(test_e2b_agent())
