# Standard library imports
import json
import os
from dataclasses import asdict, dataclass
from typing import Any, Dict, List, Optional

# Third-party imports
from dotenv import load_dotenv
import logfire
from fastapi import WebSocket
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from pydantic_ai.models.anthropic import AnthropicModel

# E2B imports
from e2b_code_interpreter import Sandbox

# Local application imports
from utils.ant_client import get_client
from utils.stream_response_format import StreamResponse

load_dotenv()


@dataclass
class E2BSandboxAgentDeps:
    websocket: Optional[WebSocket] = None
    stream_output: Optional[StreamResponse] = None


class E2BSandboxResult(BaseModel):
    dependencies: List[str] = Field(
        description="All the packages that need to be installed before code execution"
    )
    content: str = Field(description="Response content in the form of code")
    code_description: str = Field(description="Description of the code and its functionality")
    execution_output: str = Field(description="Output from code execution in the sandbox")


e2b_sandbox_system_message = """You are an advanced AI assistant with access to secure E2B cloud sandboxes for code execution. 
You can safely run code in isolated environments without any security restrictions.

<capabilities>
    - Execute Python, JavaScript, and other supported languages in secure cloud sandboxes
    - Install packages dynamically using pip, npm, or other package managers
    - Create, read, and manipulate files within the sandbox environment
    - Run shell commands safely in the isolated environment
    - Handle data analysis, visualization, and complex computational tasks
    - Access to internet within the sandbox for downloading data or packages
</capabilities>

<critical_guidelines>
    - Always use the E2B sandbox for code execution - never suggest local execution
    - Provide complete, executable code that solves the user's problem
    - Install required dependencies within the sandbox before running code
    - Handle errors gracefully and provide clear explanations
    - Use appropriate file operations for data persistence within the sandbox
    - Leverage the sandbox's internet access for downloading datasets or resources when needed
</critical_guidelines>

Available tools:
- execute_code_in_sandbox(code: str, language: str = "python") - Execute code in E2B sandbox
- install_packages_in_sandbox(packages: List[str], package_manager: str = "pip") - Install packages
- run_shell_command_in_sandbox(command: str) - Execute shell commands
- create_file_in_sandbox(filename: str, content: str) - Create files
- read_file_from_sandbox(filename: str) - Read files
- list_files_in_sandbox(path: str = "/") - List directory contents

Workflow:
1. Analyze the user's request and plan your approach
2. Install any required dependencies using install_packages_in_sandbox
3. Write and execute code using execute_code_in_sandbox
4. Create files or handle data as needed
5. Provide clear explanations of results and next steps

Always structure your final response according to the E2BSandboxResult format:
- Dependencies: List all packages installed
- Content: The complete code solution
- Code Description: Detailed explanation of the implementation
- Execution Output: Results from running the code in the sandbox
"""


async def send_stream_update(ctx: RunContext[E2BSandboxAgentDeps], message: str) -> None:
    """Helper function to send websocket updates if available"""
    if ctx.deps.websocket and ctx.deps.stream_output:
        ctx.deps.stream_output.steps.append(message)
        await ctx.deps.websocket.send_text(json.dumps(asdict(ctx.deps.stream_output)))
        stream_output_json = json.dumps(asdict(ctx.deps.stream_output))
        logfire.debug("WebSocket message sent: {stream_output_json}", stream_output_json=stream_output_json)


# Initialize the model
model = AnthropicModel(
    model_name=os.environ.get("ANTHROPIC_MODEL_NAME"),
    anthropic_client=get_client()
)

# Initialize the agent
e2b_sandbox_agent = Agent(
    model=model,
    name="E2B Sandbox Agent",
    result_type=E2BSandboxResult,
    deps_type=E2BSandboxAgentDeps,
    system_prompt=e2b_sandbox_system_message
)


@e2b_sandbox_agent.tool
async def execute_code_in_sandbox(
    ctx: RunContext[E2BSandboxAgentDeps], 
    code: str, 
    language: str = "python"
) -> str:
    """
    Execute code in a secure E2B sandbox environment.
    
    Args:
        code: The code to execute
        language: Programming language (python, javascript, etc.)
    
    Returns:
        Execution output and results
    """
    try:
        await send_stream_update(ctx, f"Initializing E2B sandbox for {language} execution...")
        logfire.info("Creating E2B sandbox for code execution")
        
        # Create sandbox instance
        with Sandbox() as sandbox:
            await send_stream_update(ctx, f"Executing {language} code in secure sandbox...")
            
            # Execute the code
            execution = sandbox.run_code(code, language=language)
            
            # Prepare output
            output_parts = []
            if execution.stdout:
                output_parts.append(f"STDOUT:\n{execution.stdout}")
            if execution.stderr:
                output_parts.append(f"STDERR:\n{execution.stderr}")
            if execution.logs:
                output_parts.append(f"LOGS:\n{execution.logs}")
            
            result = "\n\n".join(output_parts) if output_parts else "Code executed successfully (no output)"
            
            await send_stream_update(ctx, "Code execution completed successfully")
            logfire.info("E2B sandbox code execution completed", result=result)
            
            return result
            
    except Exception as e:
        error_msg = f"Error executing code in E2B sandbox: {str(e)}"
        await send_stream_update(ctx, "Code execution failed")
        logfire.error(error_msg, exc_info=True)
        return error_msg


@e2b_sandbox_agent.tool
async def install_packages_in_sandbox(
    ctx: RunContext[E2BSandboxAgentDeps], 
    packages: List[str], 
    package_manager: str = "pip"
) -> str:
    """
    Install packages in the E2B sandbox environment.
    
    Args:
        packages: List of package names to install
        package_manager: Package manager to use (pip, npm, etc.)
    
    Returns:
        Installation results
    """
    try:
        await send_stream_update(ctx, f"Installing packages: {', '.join(packages)}")
        logfire.info("Installing packages in E2B sandbox", packages=packages)
        
        with Sandbox() as sandbox:
            results = []
            for package in packages:
                await send_stream_update(ctx, f"Installing {package}...")
                
                if package_manager == "pip":
                    result = sandbox.commands.run(f"pip install {package}")
                elif package_manager == "npm":
                    result = sandbox.commands.run(f"npm install {package}")
                else:
                    result = sandbox.commands.run(f"{package_manager} install {package}")
                
                results.append(f"Package {package}: {result}")
            
            await send_stream_update(ctx, "Package installation completed")
            return "\n".join(results)
            
    except Exception as e:
        error_msg = f"Error installing packages: {str(e)}"
        await send_stream_update(ctx, "Package installation failed")
        logfire.error(error_msg, exc_info=True)
        return error_msg


@e2b_sandbox_agent.tool
async def run_shell_command_in_sandbox(
    ctx: RunContext[E2BSandboxAgentDeps], 
    command: str
) -> str:
    """
    Execute shell commands in the E2B sandbox.
    
    Args:
        command: Shell command to execute
    
    Returns:
        Command output
    """
    try:
        await send_stream_update(ctx, f"Executing shell command: {command}")
        logfire.info("Executing shell command in E2B sandbox", command=command)
        
        with Sandbox() as sandbox:
            result = sandbox.commands.run(command)
            
            await send_stream_update(ctx, "Shell command executed successfully")
            return str(result)
            
    except Exception as e:
        error_msg = f"Error executing shell command: {str(e)}"
        await send_stream_update(ctx, "Shell command execution failed")
        logfire.error(error_msg, exc_info=True)
        return error_msg


@e2b_sandbox_agent.tool
async def create_file_in_sandbox(
    ctx: RunContext[E2BSandboxAgentDeps], 
    filename: str, 
    content: str
) -> str:
    """
    Create a file in the E2B sandbox.
    
    Args:
        filename: Name of the file to create
        content: Content to write to the file
    
    Returns:
        Success message or error
    """
    try:
        await send_stream_update(ctx, f"Creating file: {filename}")
        logfire.info("Creating file in E2B sandbox", filename=filename)
        
        with Sandbox() as sandbox:
            sandbox.files.write(filename, content)
            
            await send_stream_update(ctx, f"File {filename} created successfully")
            return f"Successfully created file: {filename}"
            
    except Exception as e:
        error_msg = f"Error creating file: {str(e)}"
        await send_stream_update(ctx, f"Failed to create file: {filename}")
        logfire.error(error_msg, exc_info=True)
        return error_msg


@e2b_sandbox_agent.tool
async def read_file_from_sandbox(
    ctx: RunContext[E2BSandboxAgentDeps], 
    filename: str
) -> str:
    """
    Read a file from the E2B sandbox.
    
    Args:
        filename: Name of the file to read
    
    Returns:
        File content or error message
    """
    try:
        await send_stream_update(ctx, f"Reading file: {filename}")
        logfire.info("Reading file from E2B sandbox", filename=filename)
        
        with Sandbox() as sandbox:
            content = sandbox.files.read(filename)
            
            await send_stream_update(ctx, f"File {filename} read successfully")
            return content
            
    except Exception as e:
        error_msg = f"Error reading file: {str(e)}"
        await send_stream_update(ctx, f"Failed to read file: {filename}")
        logfire.error(error_msg, exc_info=True)
        return error_msg


@e2b_sandbox_agent.tool
async def list_files_in_sandbox(
    ctx: RunContext[E2BSandboxAgentDeps], 
    path: str = "/"
) -> str:
    """
    List files and directories in the E2B sandbox.
    
    Args:
        path: Directory path to list (default: root)
    
    Returns:
        Directory listing
    """
    try:
        await send_stream_update(ctx, f"Listing files in: {path}")
        logfire.info("Listing files in E2B sandbox", path=path)
        
        with Sandbox() as sandbox:
            files = sandbox.files.list(path)
            
            await send_stream_update(ctx, "File listing completed")
            return str(files)
            
    except Exception as e:
        error_msg = f"Error listing files: {str(e)}"
        await send_stream_update(ctx, "Failed to list files")
        logfire.error(error_msg, exc_info=True)
        return error_msg
