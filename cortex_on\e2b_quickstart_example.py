"""
E2B Sandbox Quickstart Example
This script demonstrates the basic usage of E2B sandbox integration.
"""

from dotenv import load_dotenv
load_dotenv()

from e2b_code_interpreter import Sandbox

def main():
    """
    Demonstrates basic E2B sandbox functionality following the quickstart guide.
    """
    print("🚀 Starting E2B Sandbox Quickstart Example...")
    
    try:
        # Create E2B sandbox instance
        print("📦 Creating E2B sandbox...")
        sbx = Sandbox()  # By default the sandbox is alive for 5 minutes
        
        print(f"✅ Sandbox created successfully! ID: {sbx.sandbox_id}")
        
        # Execute Python code inside the sandbox
        print("🐍 Executing Python code in sandbox...")
        execution = sbx.run_code("print('Hello world from E2B sandbox!')")
        print(f"📄 Execution logs: {execution.logs}")
        
        # List files in the root directory
        print("📁 Listing files in root directory...")
        files = sbx.files.list("/")
        print(f"📋 Files: {files}")
        
        # Create a simple Python script in the sandbox
        print("📝 Creating a Python script in sandbox...")
        script_content = """
import datetime
import platform

print(f"Current time: {datetime.datetime.now()}")
print(f"Python version: {platform.python_version()}")
print(f"Platform: {platform.platform()}")

# Simple calculation
result = sum(range(1, 101))
print(f"Sum of numbers 1-100: {result}")
"""
        
        sbx.files.write("demo_script.py", script_content)
        print("✅ Script created successfully!")
        
        # Execute the created script
        print("🔄 Executing the created script...")
        execution = sbx.run_code("exec(open('demo_script.py').read())")
        print(f"📄 Script output: {execution.logs}")
        
        # Install a package and use it
        print("📦 Installing and using a package...")
        sbx.commands.run("pip install requests")
        
        web_request_code = """
import requests
try:
    response = requests.get('https://httpbin.org/json')
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
except Exception as e:
    print(f"Error: {e}")
"""
        
        execution = sbx.run_code(web_request_code)
        print(f"📄 Web request output: {execution.logs}")
        
        # Clean up - sandbox will automatically close when the context ends
        print("🧹 Cleaning up...")
        # Note: Sandbox cleanup is automatic, no need to call close() manually
        print("✅ E2B Sandbox example completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure you have set the E2B_API_KEY in your .env file")
        print("Get your API key from: https://e2b.dev/dashboard")


if __name__ == "__main__":
    main()
