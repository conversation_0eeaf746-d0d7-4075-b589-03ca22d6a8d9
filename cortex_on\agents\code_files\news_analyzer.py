import json
from textblob import TextBlob
import re
from collections import defaultdict

# Sample news headlines data (in real scenario, this would be loaded from a file)
news_data = [
    {"source": "BBC", "headline": "Global economy shows signs of recovery amid pandemic"},
    {"source": "CNN", "headline": "Tech stocks soar as remote work continues to drive growth"},
    {"source": "Reuters", "headline": "Climate change causes severe flooding in coastal regions"},
    {"source": "BBC", "headline": "New vaccine breakthrough provides hope for COVID-19 treatment"},
    {"source": "CNN", "headline": "Cybersecurity threats increase as digital transformation accelerates"},
]

def clean_headline(headline):
    # Remove special characters and extra whitespace
    cleaned = re.sub(r'[^\w\s]', '', headline)
    cleaned = ' '.join(cleaned.split())
    return cleaned.lower()

def get_sentiment(headline):
    # Analyze sentiment using TextBlob
    blob = TextBlob(headline)
    return {
        'polarity': blob.sentiment.polarity,
        'subjectivity': blob.sentiment.subjectivity
    }

def categorize_headline(headline):
    # Define keywords for different categories
    categories = {
        'technology': ['tech', 'digital', 'cyber', 'software', 'ai'],
        'health': ['covid', 'vaccine', 'health', 'medical', 'treatment'],
        'economy': ['economy', 'market', 'stocks', 'financial', 'business'],
        'environment': ['climate', 'environment', 'pollution', 'sustainable'],
    }
    
    headline_lower = headline.lower()
    for category, keywords in categories.items():
        if any(keyword in headline_lower for keyword in keywords):
            return category
    return 'other'

def analyze_news():
    # Initialize data structures for analysis
    results = {
        'headlines_analysis': [],
        'category_sentiment': defaultdict(list),
        'source_sentiment': defaultdict(list)
    }
    
    # Process each headline
    for item in news_data:
        headline = item['headline']
        source = item['source']
        
        # Clean and analyze the headline
        cleaned_headline = clean_headline(headline)
        sentiment = get_sentiment(headline)
        category = categorize_headline(headline)
        
        # Store individual headline analysis
        headline_analysis = {
            'original': headline,
            'cleaned': cleaned_headline,
            'source': source,
            'category': category,
            'sentiment': sentiment
        }
        results['headlines_analysis'].append(headline_analysis)
        
        # Aggregate sentiment by category and source
        results['category_sentiment'][category].append(sentiment['polarity'])
        results['source_sentiment'][source].append(sentiment['polarity'])
    
    # Calculate average sentiments
    category_avg = {category: sum(scores)/len(scores) 
                   for category, scores in results['category_sentiment'].items()}
    source_avg = {source: sum(scores)/len(scores) 
                 for source, scores in results['source_sentiment'].items()}
    
    # Convert defaultdict to regular dict for JSON serialization
    results['category_sentiment'] = category_avg
    results['source_sentiment'] = source_avg
    
    return results

# Run analysis and print results
if __name__ == "__main__":
    results = analyze_news()
    print(json.dumps(results, indent=2))