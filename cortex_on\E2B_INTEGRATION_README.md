# E2B Sandbox Integration

This document describes the E2B sandbox integration in CortexON, providing secure cloud-based code execution capabilities.

## Overview

E2B (Execute to Build) provides secure, isolated cloud sandboxes for running AI-generated code. This integration allows CortexON to execute code safely in the cloud without local security restrictions.

## Features

- **Secure Code Execution**: Run Python, JavaScript, and other languages in isolated cloud environments
- **Package Installation**: Dynamically install packages using pip, npm, or other package managers
- **Internet Access**: Sandboxes have internet connectivity for downloading data and resources
- **File Operations**: Create, read, and manipulate files within the sandbox environment
- **Multiple Languages**: Support for Python, JavaScript, Java, and more
- **Real-time Streaming**: Live updates of execution progress through WebSocket connections

## Setup

### 1. Get E2B API Key

1. Visit [E2B Dashboard](https://e2b.dev/dashboard)
2. Create an account (new accounts get $100 in credits)
3. Copy your API key

### 2. Configure Environment

Add your E2B API key to the `.env` file:

```env
E2B_API_KEY=e2b_your_api_key_here
```

### 3. Install Dependencies

The E2B SDK is already included in `requirements.txt`:

```bash
pip install -r requirements.txt
```

## Usage

### Through the Orchestrator Agent

The E2B sandbox agent is integrated into the orchestrator and can be accessed through the `e2b_sandbox_task` tool:

```python
# The orchestrator will automatically choose the E2B sandbox for complex tasks
await orchestrator_agent.run("Analyze this dataset and create visualizations")
```

### Direct Usage

You can also use the E2B sandbox agent directly:

```python
from agents.e2b_sandbox_agent import e2b_sandbox_agent, E2BSandboxAgentDeps

# Create dependencies
deps = E2BSandboxAgentDeps(websocket=websocket, stream_output=stream_output)

# Run the agent
result = await e2b_sandbox_agent.run(
    user_prompt="Create a data analysis script for sales data",
    deps=deps
)
```

### Available Tools

The E2B sandbox agent provides several tools:

1. **execute_code_in_sandbox**: Execute code in the sandbox
2. **install_packages_in_sandbox**: Install packages dynamically
3. **run_shell_command_in_sandbox**: Execute shell commands
4. **create_file_in_sandbox**: Create files in the sandbox
5. **read_file_from_sandbox**: Read files from the sandbox
6. **list_files_in_sandbox**: List directory contents

## Examples

### Basic Code Execution

```python
# This will be executed in a secure E2B sandbox
task = """
Create a Python script that:
1. Installs pandas and matplotlib
2. Generates sample sales data
3. Creates a visualization
4. Saves the plot as an image
"""

result = await e2b_sandbox_agent.run(task)
```

### Data Analysis

```python
task = """
Analyze the following CSV data:
1. Load the data from a URL
2. Perform statistical analysis
3. Create multiple visualizations
4. Generate a summary report
"""

result = await e2b_sandbox_agent.run(task)
```

### Machine Learning

```python
task = """
Build a machine learning model:
1. Install scikit-learn and necessary packages
2. Load a dataset from sklearn
3. Train a classification model
4. Evaluate the model performance
5. Create visualizations of the results
"""

result = await e2b_sandbox_agent.run(task)
```

## Quickstart Example

Run the included quickstart example to test the integration:

```bash
cd cortex_on
python e2b_quickstart_example.py
```

This example demonstrates:
- Creating an E2B sandbox
- Executing Python code
- Installing packages
- Making web requests
- File operations

## When to Use E2B vs Local Code Agent

### Use E2B Sandbox Agent for:
- Complex data analysis and visualization
- Machine learning tasks
- Tasks requiring package installation
- Web scraping or API calls
- Long-running computations
- Tasks requiring internet access
- Multi-file projects

### Use Local Code Agent for:
- Simple file operations
- Basic calculations
- Quick scripts without dependencies
- Tasks that don't require external packages

## Architecture

```
User Request
     ↓
Orchestrator Agent
     ↓
E2B Sandbox Agent
     ↓
E2B Cloud Sandbox
     ↓
Execution Results
```

## Security

- **Isolated Environment**: Each sandbox runs in complete isolation
- **No Local Access**: Code execution happens in the cloud, not locally
- **Automatic Cleanup**: Sandboxes are automatically destroyed after use
- **Resource Limits**: Built-in limits prevent resource abuse

## Monitoring and Logging

- All E2B operations are logged using logfire
- Real-time progress updates through WebSocket connections
- Execution results and errors are captured and reported
- Sandbox lifecycle events are tracked

## Troubleshooting

### Common Issues

1. **API Key Not Set**
   - Ensure `E2B_API_KEY` is set in your `.env` file
   - Verify the API key is correct

2. **Package Installation Fails**
   - Check package names for typos
   - Some packages may not be available in the sandbox environment

3. **Timeout Errors**
   - Long-running tasks may timeout
   - Consider breaking large tasks into smaller chunks

4. **Memory/Resource Limits**
   - E2B sandboxes have resource limits
   - Optimize code for memory efficiency

### Getting Help

- Check the [E2B Documentation](https://e2b.dev/docs)
- Review the E2B sandbox logs in the application
- Contact E2B support for sandbox-specific issues

## Cost Considerations

- New E2B accounts receive $100 in credits
- Sandbox usage is billed based on execution time
- Monitor usage through the E2B dashboard
- Consider optimizing code to reduce execution time

## Future Enhancements

- Support for custom sandbox templates
- Persistent storage across sandbox sessions
- Integration with additional programming languages
- Enhanced debugging capabilities
- Collaborative sandbox sharing
